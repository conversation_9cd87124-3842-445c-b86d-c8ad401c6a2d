{"$schema": "https://schema.tauri.app/config/2", "productName": "black-hole", "version": "0.1.0", "identifier": "com.black-hole.app", "build": {"beforeDevCommand": "yarn dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "yarn build", "frontendDist": "../dist"}, "app": {"windows": [{"title": "Block Hole", "width": 800, "height": 600}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}