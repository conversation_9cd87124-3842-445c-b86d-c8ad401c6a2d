use pnet_packet::ipv4::Ipv4Packet;
use tun::{AbstractDevice, Configuration};

// Learn more about Tauri commands at https://tauri.app/develop/calling-rust/
#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

#[tauri::command]
async fn start_tun() -> Result<(), String> {
    println!("start tun listener");

    let mut config = Configuration::default();
    config
        .address((10, 0, 0, 100))
        .destination((10, 0, 0, 1))
        .netmask((255, 255, 255, 0))
        .mtu(1500)
        .up();

    let dev = tun::create_as_async(&config).unwrap();
    println!("tun device created. name: {:?}", dev.tun_name());

    tokio::spawn(async move {
        let mut buf = [0u8; 1600];

        loop {
            let n = dev.recv(&mut buf).await.unwrap();
            println!("read {} bytes", n);

            let Some(packet) = Ipv4Packet::new(&buf[..n]) else {
                println!("invalid packet");

                continue;
            };

            println!("packet: {:?}", packet.get_source());
        }
    });

    Ok(())
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .invoke_handler(tauri::generate_handler![greet, start_tun])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
